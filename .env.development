# 本地环境
ENV = development

# 本地环境接口地址 - IoT系统Java后端
VITE_API_URL = http://*************:6106/
# VITE_API_URL = http://iot.fastbee.cn:8080/

# 开发环境
VITE_APP_BASE_API = '/prod-api'

# FastAPI服务地址（用于知识库等特定功能）
VITE_FASTAPI_URL = http://localhost:8000


# Mqtt消息服务器连接地址
VITE_MQTT_URL = 'ws://*************:5976/mqtt'

# 百度地图AK
VUE_APP_BAI_DU_AK = 'nAtaBg9FYzav6c8P9rF9qzsWZfT8O0PD'
# .env.development
# VITE_APP_BASE_API=http://localhost:8080/api
# VITE_APP_TITLE="Vue Next Admin - Development"
# VITE_APP_ENV=development
# VITE_PORT=3000
# VITE_APP_MQTT_SERVER_URL=ws://localhost:8083/mqtt
# VUE_APP_BAI_DU_AK=your_baidu_ak

VITE_OPEN_CDN=false
